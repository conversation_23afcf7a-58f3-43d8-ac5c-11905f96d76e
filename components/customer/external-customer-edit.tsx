"use client";

import React, { useState, useEffect } from "react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import apiClient from "@/lib/apiClient";
import { useAuth } from "@/context/AuthContext";
import { toast } from "sonner";

interface CustomerEditFormProps {
  customer: any;
  onCancel: () => void;
  onSave?: (updatedCustomerData: any) => void;
}

export interface Packages {
  id: number;
  package_name: string;
}
export type UserPackages = string;

export default function CustomerEditForm({
  customer,
  onCancel,
  onSave,
}: CustomerEditFormProps) {
  const [form, setForm] = useState({
    pack_id: "",
    username: "",
  });

  const [originalForm, setOriginalForm] = useState<typeof form>(form);
  const [packages, setPackages] = useState<Packages[]>([]);
  const { setIsRefreshed } = useAuth();

  useEffect(() => {
    if (customer) {
      const initial = {
        pack_id: customer?.pack_id || "",
        username: customer?.username || "",
      };
      setForm(initial);
      setOriginalForm(initial);
    }
  }, [customer]);

  const handleChange = (field: string, value: string) => {
    setForm((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  //const getChangedFields = (original: any, updated: any) => {
  //  const diff: Record<string, any> = {};
  //  for (const key in updated) {
  //    if (key === "confirmPassword") continue;
  //    if (updated[key] !== original[key]) {
  //      if (key === "password" && updated[key] === "") continue;
  //      diff[key] = updated[key];
  //    }
  //  }
  //  return diff;
  //};

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();


  //  const changedFields = getChangedFields(originalForm, form);
    //fields=form
    //if (Object.keys(changedFields).length === 0) {
    //  toast.error("No changes detected");
    //  return;
    //}

    try {
      const response = await apiClient.post(
        `/ecustomer`,
	form
      );

      // Backend returns: { success: true, message: "Package has been updated" }
      setIsRefreshed((prev) => !prev);
      toast.success("Staff updated successfully");
      onSave ? onSave(form) : onCancel();
    } catch (error) {
      console.error("Failed to edit customer:", error);
      toast.error(error.message || "Update failed");
    }
  };

  useEffect(() => {
    const fetchPackages = async () => {
      try {
        const response = await apiClient.get("/package");
        // Backend returns: { success: true, message: "List Package", data: [...] }
        // The apiClient response interceptor extracts the data property
        setPackages(response?.data || []);
      } catch (error) {
        console.error("Failed to fetch packages:", error);
        toast.error(error.message || "Failed to fetch packages");
      }
    };

    fetchPackages();
  }, []);

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
      <div className="fixed inset-0 bg-black bg-opacity-60 backdrop-blur-sm" />
      <div className="relative bg-white rounded-xl shadow-2xl overflow-hidden max-w-md md:max-w-[40rem] w-full max-h-[95vh] flex flex-col">
      <div className="px-5 py-6 bg-gradient-to-r bg-primary text-white border-b">
      <h2 className="text-lg font-extrabold text-center">
        Link Staff: {form.username} to Department
      </h2>
      </div>
      <form onSubmit={handleSubmit} className="flex-grow p-10 grid grid-cols-1 gap-x-6 gap-y-4 overflow-y-auto custom-scrollbar space-y-3">
        <div className="text-xs grid grid-cols-1 md:grid-cols-2 gap-4">


          <div>
            <Label htmlFor="username">Username</Label>
            <Input
              id="username"
              type="text"
              value={form.username}
              disabled
              onChange={(e) => handleChange("username", e.target.value)}
            />
          </div>




          <div className="space-y-1">
            <Label className="text-sm font-medium text-gray-700">
              Department
            </Label>
            <select
              className="w-full p-2 border rounded-md"
              value={form.pack_id}
              onChange={(e) => handleChange("pack_id", e.target.value)}
              required
            >
              <option value="">Select a Department</option>
              {packages.map((pkg) => (
                <option key={pkg.id} value={pkg.id.toString()}>
                  {pkg.package_name}
                </option>
              ))}
            </select>
          </div>

          <div className="space-y-2">
            <Label>Status *</Label>
            <div className="flex gap-4">
              {/* Active Button */}
              <button
                type="button"
                aria-pressed={form.status === "Active"}
                className={`w-20 h-8 border-2 rounded-lg flex items-center justify-center
          ${form.status === "Active"
                    ? "border-blue-600 bg-blue-600 text-white"
                    : "border-gray-300 bg-white text-black"
                  }
          hover:border-blue-400 transition-colors font-medium`}
                onClick={() => handleChange("status", "Active")}
              >
                Active
              </button>

              {/* Inactive Button */}
              <button
                type="button"
                aria-pressed={form.status === "Inactive"}
                className={`w-20 h-8 border-2 rounded-lg flex items-center justify-center
          ${form.status === "Inactive"
                    ? "border-blue-600 bg-blue-600 text-white"
                    : "border-gray-300 bg-white text-black"
                  }
          hover:border-blue-400 transition-colors font-medium`}
                onClick={() => handleChange("status", "Inactive")}
              >
                Inactive
              </button>
            </div>
          </div>
        </div>

        <div className="flex flex-col justify-end sm:flex-row gap-2 mt-4">
          <Button size="sm" type="button" variant="outline" onClick={onCancel}>
            Cancel
          </Button>
          <Button type="submit" className="w-full sm:w-auto" size="sm">
            Save Changes
          </Button>
        </div>
      </form>
      </div>
    </div>
  );
}
