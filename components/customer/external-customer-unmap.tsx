"use client";

import React, { useState, useRef, useEffect } from "react";
import { Input } from "@/components/ui/input";
import Link from "next/link";

import {
  ChevronRight,
  ChevronLeft,
  Plug,
} from "@/components/icons/list";
import CustomerEdit from "@/components/customer/external-customer-edit";
import { Button } from "@/components/ui/button";

import apiClient from "@/lib/apiClient";
import { usePathname } from "next/navigation";
import { useAuth } from "@/context/AuthContext";
import { toast } from "sonner";





export default function ExternalCustomersUnmapped() {
  interface Customer {
    id: number;
    first_name: string;
    last_name: string;
    username: string;
    password: string;
    email: string;
    status: string;
    package: string;
    pack_id?: string;
  }


  const [currentPage, setCurrentPage] = useState(1);
  const [currentFilter, setCurrentFilter] = useState<string>("All");
  const [selectedItemsPerPageValue, setSelectedItemsPerPageValue] =
    useState<string>("15");

  const componentRef = useRef<HTMLDivElement>(null);
  const [customer, setCustomer] = useState<Customer[]>([]);
  const [isEditOpen, setIsEditOpen] = useState(false);
  const [isEditCustomer, setIsEditCustomer] = useState(false);
  const pathname = usePathname();

  const [isLoading, setIsLoading] = useState(false);
  const loadingTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const { isRefreshed } = useAuth();
  const [search, setSearch] = useState("");

  const startDelayedLoading = () => {
    if (loadingTimeoutRef.current) {
      clearTimeout(loadingTimeoutRef.current);
    }
    loadingTimeoutRef.current = setTimeout(() => {
      setIsLoading(true);
    }, 200);
  };

  const stopLoading = () => {
    if (loadingTimeoutRef.current) {
      clearTimeout(loadingTimeoutRef.current);
      loadingTimeoutRef.current = null;
    }
    setIsLoading(false);
  };

  useEffect(() => {
    stopLoading();
  }, [pathname]);

  useEffect(() => {
    const fetchCustomer = async () => {
      try {
        const response = await apiClient.get(`/auth/postauth/`);
        // Backend returns: { success: true, message: "List Customer", data: [...] }
        // The apiClient response interceptor extracts the data property
        setCustomer(response?.data || []);
      } catch (error) {
        console.error("Failed to fetch customers:", error);
        toast.error((error as any)?.message || "Failed to fetch staff");
      }
    };

    fetchCustomer();
  }, [isRefreshed]);



  const itemsPerPage =
    selectedItemsPerPageValue === "all"
      ? customer?.length
      : parseInt(selectedItemsPerPageValue, 10);

  const filteredCustomers = customer?.filter((customer: Customer) => {
    const matchesSearch =
      customer?.username.toLowerCase().includes(search.toLowerCase());

    // Filter by status
    const matchesStatus =
      currentFilter === "All" || customer?.status === currentFilter;

    return matchesSearch && matchesStatus;
  });

  const sortedCustomers = [...(filteredCustomers || [])].sort((a, b) => {
    const usernameA = a.username.toLowerCase();
    const usernameB = b.username.toLowerCase();
    if (usernameA < usernameB) {
      return -1;
    }
    if (usernameA > usernameB) {
      return 1;
    }
    return 0;
  });

  const totalPages =
    itemsPerPage === 0 ? 1 : Math.ceil(sortedCustomers.length / itemsPerPage);

  const currentCustomers = sortedCustomers.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );

  const scrollToTop = () => {
    if (componentRef.current) {
      // Find the closest parent with overflow-y: auto or scroll
      let currentElement: HTMLElement | null = componentRef.current;
      while (currentElement && currentElement !== document.body) {
        const style = window.getComputedStyle(currentElement);
        // Ensure the element has scrollable content and is visually scrollable
        if (
          (style.overflowY === "auto" || style.overflowY === "scroll") &&
          currentElement.scrollHeight > currentElement.clientHeight
        ) {
          currentElement.scrollTop = 0;
          return; // Found and scrolled the container
        }
        currentElement = currentElement.parentElement;
      }
      // Fallback to window scroll if no specific scrollable parent is found within the component's hierarchy
      window.scrollTo({ top: 0, behavior: "smooth" });
    }
  };

  useEffect(() => {
    scrollToTop();
  }, [currentPage]);

  const handleFilterClick = (filter: string) => {
    setCurrentFilter(filter);
    setCurrentPage(1);
  };

  const handleItemsPerPageChange = (
    e: React.ChangeEvent<HTMLSelectElement>
  ) => {
    const value = e.target.value;
    setSelectedItemsPerPageValue(value);
    setCurrentPage(1);
  };

  return (
    <div className="space-y-3">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center flex-wrap gap-4">
        <h1 className="text-lg sm:text-lg font-bold sm:mb-3 text-wrap">
          External DB unmapped staff
        </h1>

        <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-3 w-full sm:w-auto">
          <Input // Using Input component for consistency
            type="text"
            placeholder="Search User"
            value={search}
            onChange={(e) => setSearch(e.target.value)}
            className="w-full sm:w-[250px] p-1 border border-gray-300 rounded-full px-3 focus:ring focus:border-blue-300"
          />
        </div>
      </div>
      <div
        ref={componentRef}
        className="mx-auto bg-white rounded-lg p-5 sm:p-6 lg:p-5 max-w-full"
      >
        {/* Loading Spinner */}
        {isLoading && (
          <div className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-[9999]">
            <div className="flex space-x-2">
              <div className="w-3 h-3 bg-white rounded-full animate-bounce-slow"></div>
              <div className="w-3 h-3 bg-white rounded-full animate-bounce-medium"></div>
              <div className="w-3 h-3 bg-white rounded-full animate-bounce-fast"></div>
            </div>
          </div>
        )}

        {/* Customer List */}
        <>
          <div className="w-full mb-4">
            <div className="flex flex-col sm:flex-row justify-between items-center gap-4 mb-4">
              <div className="flex flex-col sm:flex-row items-center gap-4">
                <div
                  className={`bg-white px-3 py-1 rounded-md  border ${currentFilter === "All"
                    ? "border-blue-300 ring-blue-200"
                    : "border-blue-200 hover:border-blue-300"
                    } text-center cursor-pointer transition-all duration-200`}
                  onClick={() => handleFilterClick("All")}
                >
                  <h3 className="text-sm font-medium text-blue-700 ">
                    Total Staff:
                    <span className="text-sm font-bold text-blue-800">
                      {customer?.length}
                    </span>
                  </h3>
                </div>

                {/* Search Input */}
              </div>

              {/* Right Side: Show entries dropdown */}
              <div className="flex items-center gap-2 text-xs">
                <span>Show</span>
                <select
                  value={selectedItemsPerPageValue}
                  onChange={handleItemsPerPageChange}
                  className="border border-gray-300 rounded-md p-1.5 h-7 text-xs focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="15">15</option>
                  <option value="30">30</option>
                  <option value="50">50</option>
                  <option value="100">100</option>
                  <option value="all">All</option>
                </select>
                <span>entries</span>
              </div>
            </div>
          </div>

          <div className="w-full overflow-x-auto">
            <table className="min-w-max w-full">
              <thead className="bg-gray-200 text-left text-xs uppercase align-middle">
                <tr>
                  <th className=" px-4 sm:px-4 py-2 hidden md:table-cell">
                    S.N.
                  </th>
                  <th className=" px-4 sm:px-4 py-2">Username</th>
                  <th className=" px-4 sm:px-4 py-2">Authenticated on</th>
                  <th className=" px-4 sm:px-4 py-2 text-end">Action</th>
                </tr>
              </thead>
              <tbody>
                {currentCustomers?.length === 0 ? (
                  <tr key="no-customers">
                    <td
                      colSpan={7}
                      className="px-1 sm:px-2 py-8 text-center text-blue-900 text-sm"
                    >
                      Oops! No user matched your search
                    </td>
                  </tr>
                ) : (
                  currentCustomers.map((customer, index) => (
                    <tr
                      key={index}
                      className="border-b"
                    >
                      <td className="px-4 sm:px-4 py-1 text-xs hidden md:table-cell">
                        {(currentPage - 1) * itemsPerPage + index + 1}
                      </td>

                      <td
                        className="px-4 sm:px-4 py-1 text-xs text-blue-600  cursor-pointer"
                      >
                        <Link
                          href={`/app/staff/view?id=${customer.id}`}
                          className="font-medium text-blue-500"
                          onClick={(e) => {
                            e.stopPropagation();
                            startDelayedLoading();
                          }}
                        >
                          {customer?.username}
                        </Link>

                      </td>
                      <td className="px-4 sm:px-4 py-1 text-xs">
                        {customer?.auth_date}
                      </td>

                      <td className="px-4 sm:px-4 py-1 text-xs">
                        <div className="flex items-center justify-end gap-1">
                          <Button
                            className="bg-green-500 hover:bg-green-600 text-white p-1 rounded-md h-7 w-7"
                            onClick={() => {
                              setIsEditOpen(true);
                              setIsEditCustomer(customer);
                            }}
                          >
                            <Plug />
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>

          {totalPages > 1 && (
            <div className="flex items-center gap-1 justify-left mt-4">
              <Button
                className="rounded-full w-8 h-6"
                size="ss"
                onClick={() => setCurrentPage((p) => Math.max(p - 1, 1))}
                disabled={currentPage === 1}
              >
                <ChevronLeft />
              </Button>
              <span className="text-[12px]">
                Page {currentPage} of {totalPages}
              </span>
              <Button
                className="rounded-full w-8 h-6"
                size="ss"
                onClick={() =>
                  setCurrentPage((p) => Math.min(p + 1, totalPages))
                }
                disabled={currentPage === totalPages}
              >
                <ChevronRight />
              </Button>
            </div>
          )}
        </>
      </div>

      {isEditOpen && (

        <CustomerEdit
          customer={isEditCustomer}
          onCancel={() => setIsEditOpen(false)}
        />
      )}

    </div>
  );
}


