"use client";

import { useState, useEffect, React } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
// import apiClient from "@/lib/apiClient";
import { Group } from "./add-user";
// import { Organization } from "./add-user";
import { useFetch } from "@/hooks/useFetchOnMount";
import { Eye, EyeOff } from "@/components/icons/list";

export interface EditUserFormProps {
  user: any;
  onSubmit: (user: any) => void;
  onClose: () => void;
}

export function EditUserForm({ user, onSubmit, onClose }: EditUserFormProps) {
  const [newPassword, setNewPassword] = useState(false);
  const [confirmPassword, setConfirmPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [showConfirmPasswordText, setShowConfirmPasswordText] = useState(false);
  const [isValid, setIsValid] = useState(false);
  const [confirmPasswordError, setConfirmPasswordError] = useState("");

  const { data: group, loading: loading1, error } = useFetch("/groups");
  let groups: Group[] = group;
  const fileteredGroupid = groups?.find((grp) => grp?.name === user?.group)?.id;
  const [formData, setFormData] = useState({
    username: user?.username,
    password: "",
    email: user?.email,
    status: user?.status,
    group: fileteredGroupid,
  });
  useEffect(() => {
    if (groups && user?.group) {
      const userGroup = groups.find((grp) => grp?.name === user.group);
      if (userGroup) {
        setFormData((prev) => ({ ...prev, group: userGroup.id }));
      }
    }
  }, [groups, user?.group]);
  // const isValid =
  // formData.username.trim() !== "" &&
  // (newPassword === "" || newPassword === confirmPassword);

  const getChangedFields = () => {
    const changed: Record<string, any> = { id: user?.id };

    for (const key in formData) {
      if (key === "password") {
        if (formData?.password && formData?.password.trim() !== "") {
          changed.password = formData?.password;
        }
        continue;
      }
      if (formData[key] !== user?.[key]) {
        changed[key] = formData[key];
      }
    }

    return changed;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    const changed = getChangedFields();
    onSubmit?.(changed);
  };

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>
  ) => {
    const { name, value } = e.target;
    setIsValid(true);
    setFormData((prev) => ({ ...prev, [name]: value }));
    if (name === "password") {
      setShowConfirmPassword(true);
    }
    // setError(null);
  };

  useEffect(() => {
    if (formData.password || confirmPassword) {
      if (formData.password !== confirmPassword) {
        setConfirmPasswordError("Passwords do not match.");
        setIsValid(false);
      } else {
        setConfirmPasswordError("");
        setIsValid(true);
      }
    } else {
      setConfirmPasswordError("");
      setIsValid(true);
    }
  }, [formData.password, confirmPassword]);

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4">

      <div className="fixed inset-0 bg-black bg-opacity-60 backdrop-blur-sm" />
      <div className="relative bg-white rounded-xl  shadow-2xl overflow-hidden max-w-md md:max-w-lg w-full max-h-[90vh] flex flex-col">

        <div className="px-5 py-6 bg-gradient-to-r bg-primary text-white border-b">

          <h1 className="text-lg font-extrabold text-center">Edit User</h1>
          <p className="text-blue-100 text-sm  text-center">
            Update user information for
            <strong>{user?.username || "Unknown User"}</strong>
          </p>
        </div>
        <form
          onSubmit={handleSubmit}
          className="flex-grow p-10 grid grid-cols-1 gap-x-6 gap-y-5 overflow-y-auto custom-scrollbar"
        // className="flex-grow p-5 grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-5 overflow-y-auto custom-scrollbar"
        >
          <div className="space-y-2">
            <label
              htmlFor="username"
              className="block text-xs font-semibold text-gray-700"
            >
              Username
            </label>
            <Input
              value={formData?.username}
              name="username"
              onChange={handleChange}
              placeholder="Username"
            />
          </div>

          <div className="relative space-y-2">
            <label
              htmlFor="newPassword"
              className="block text-xs font-semibold text-gray-700 "
            >
              New Password
            </label>
            <Input
              type={showPassword ? "text" : "password"}
              name="password"
              value={formData?.password}
              onChange={handleChange}
              placeholder="New Password (leave blank to keep current)"
            />
            <Button
              type="button"
              variant="ghost"
              size="sm"
              className="absolute right-2.5 top-1/2 -translate-y-1.5 h-8 px-2 py-1 text-gray-500 hover:bg-gray-200 rounded-md"
              onClick={() => setShowPassword(!showPassword)}
            >
              {showPassword ? (
                <EyeOff className="h-4 w-4" />
              ) : (
                <Eye className="h-4 w-4" />
              )}
            </Button>
          </div>

          {showConfirmPassword && (
            <div className="space-y-2">
              <label className="text-xs font-medium text-gray-700">
                Confirm New Password
              </label>

              <div className="relative">
                <Input
                  type={showConfirmPasswordText ? "text" : "password"}
                  placeholder="Confirm New Password"
                  value={confirmPassword}
                  onChange={(e) => setConfirmPassword(e.target.value)}
                />

                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="absolute right-2 top-1/4 -translate-y-1 h-8"
                  onClick={() =>
                    setShowConfirmPasswordText(!showConfirmPasswordText)
                  }
                >
                  {showConfirmPasswordText ? (
                    <EyeOff className="h-4 w-4" />
                  ) : (
                    <Eye className="h-4 w-4" />
                  )}
                </Button>
              </div>

              {confirmPasswordError && (
                <p className="text-xs text-red-500">{confirmPasswordError}</p>
              )}
            </div>
          )}

          <div className="space-y-2">
            <label
              htmlFor="email"
              className="block text-xs font-semibold text-gray-700"
            >
              Email Address
            </label>
            <Input
              id="email"
              type="email"
              name="email"
              placeholder="e.g. <EMAIL>"
              value={formData?.email}
              onChange={handleChange}
              pattern="[a-z0-9._%+-]+@[a-z0-9.-]+\.[a-z]{2,}$"
            />
            {formData?.email &&
              !formData?.email.match(/^[^\s@]+@[^\s@]+\.[^\s@]+$/) && (
                <span className="text-xs text-red-500 mt-1 block">
                  Please enter a valid email address.
                </span>
              )}
          </div>
          <GroupSelect
            value={formData?.group_id}
            onChange={handleChange}
            groups={groups}
            loading={loading1}
          />

          <div className="space-y-2">
            <label
              htmlFor="status"
              className="block text-xs font-semibold text-gray-700"
            >
              Status
            </label>
            <select
              className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent transition duration-200 ease-in-out text-gray-800"
              value={formData?.status}
              name="status"
              onChange={handleChange}
              required
            >
              <option value="active">Active</option>
              <option value="inactive">Inactive</option>
            </select>
          </div>
          <div className="md:col-span-1 flex justify-end items-center gap-3 pt-3 border-t border-gray-200 mt-3">
            <Button variant="custom" onClick={onClose}>
              Cancel
            </Button>
            <Button
              type="submit"
              onClick={handleSubmit}
              className="text-white px-6 py-2.5 rounded-md"
              disabled={!isValid}
            >
              Save Changes
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
}

function GroupSelect({
  value,
  onChange,
  groups,
  loading,
}: {
  value: string;
  onChange: (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>
  ) => void;
  groups: Group[];
  loading: boolean;
}) {
  return (
    <div className="space-y-1">
      <label
        htmlFor="group"
        className="block text-xs font-semibold text-gray-700"
      >
        Group
      </label>
      <select
        className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent transition duration-200 ease-in-out text-gray-800 text-xs"
        value={value}
        onChange={onChange}
        required
      >
        {/* <option value="">Select a group</option> */}
        {groups?.map((group) => (
          <option key={group?.id} value={group?.id} name="group">
            {group?.name}
          </option>
        ))}
      </select>
    </div>
  );
}
